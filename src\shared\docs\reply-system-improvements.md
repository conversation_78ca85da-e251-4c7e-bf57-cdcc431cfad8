# Reply System Improvements

## Tổng quan

Tài liệu này mô tả các cải tiến đã thực hiện cho hệ thống reply để hỗ trợ API response mới với cấu trúc `ThreadMessageResponseDto`.

## Vấn đề trước đây

1. **Tìm kiếm reply content không chính xác**: Function `findReplyContent` chỉ tìm theo `msg.id` (local ID) thay vì `msg.messageId` (API ID)
2. **Không tìm được reply content từ history**: Khi reply một message trong history, system không thể tìm thấy content
3. **Thiếu debug information**: Khó debug khi reply không hoạt động

## Giải pháp đã triển khai

### 1. Cải tiến `findReplyContent` function

**File**: `src/shared/utils/reply-helper.utils.ts`

```typescript
// Trước đây
const replyMessage = messages.find(msg => msg.id === replyToMessageId);

// Bây giờ - hỗ trợ cả API ID và local ID
const replyMessage = messages.find(msg => 
  msg.messageId === replyToMessageId || msg.id === replyToMessageId
);
```

**Lợi ích**:
- Hỗ trợ tìm kiếm theo cả `messageId` (từ API) và `id` (local)
- Tương thích với cả current messages và history messages

### 2. Cải tiến `extractReplyInfo` function

**Thêm parameter `historyMessages`**:

```typescript
export function extractReplyInfo(
  message: ChatMessage,
  allMessages?: ChatMessage[],
  historyMessages?: ChatMessage[] // ← Thêm parameter mới
): { replyToMessageId: string; replyContent?: string } | undefined
```

**Logic tìm kiếm**:
1. Ưu tiên `replyContent` từ metadata (từ API)
2. Tìm trong `allMessages` (current messages)
3. Tìm trong `historyMessages` (history messages)

### 3. Cập nhật ChatContent component

**File**: `src/shared/components/layout/chat-panel/ChatContent.tsx`

```typescript
// Truyền cả historyMessages vào extractReplyInfo
const replyInfo = extractReplyInfo(messageData, messages, historyMessages);
```

### 4. Cải tiến useChatStream hook

**File**: `src/shared/hooks/common/useChatStream.ts`

**Cải tiến xử lý user message với reply**:
- Tìm reply content từ cả current messages và history messages
- Lưu reply content vào metadata ngay khi tạo user message

```typescript
// Tìm reply content từ messages hiện tại nếu có replyToMessageId
let replyContent: string | undefined;
if (replyToMessageId) {
  const allCurrentMessages = [...historyMessages, ...messages];
  const replyMessage = allCurrentMessages.find(msg => 
    msg.messageId === replyToMessageId || msg.id === replyToMessageId
  );
  if (replyMessage) {
    replyContent = typeof replyMessage.content === 'string' 
      ? replyMessage.content 
      : String(replyMessage.content);
  }
}
```

### 5. Cải tiến convertThreadMessageToChatMessage

**File**: `src/shared/types/chat-streaming.types.ts`

**Đảm bảo messageId được set**:
```typescript
const chatMessage: ChatMessage = {
  id: threadMessage.messageId,
  content: textContent,
  sender: threadMessage.role === 'user' ? 'user' : 'assistant',
  timestamp: new Date(threadMessage.timestamp),
  status: MessageStatus.COMPLETED,
  threadId: threadMessage.threadId,
  messageId: threadMessage.messageId, // ← Đảm bảo messageId được set
  metadata
};
```

## Debug Features

### Console Logging

Thêm debug logs để theo dõi quá trình:

1. **findReplyContent**: Log quá trình tìm kiếm message
2. **extractReplyInfo**: Log kết quả extract reply info

### Test Coverage

Tạo test file `src/shared/utils/__tests__/reply-helper.test.ts` để đảm bảo:
- Tìm kiếm theo API ID hoạt động
- Tìm kiếm theo local ID hoạt động  
- Tìm kiếm trong history messages hoạt động
- Extract reply info từ metadata hoạt động

## API Response Format

Hệ thống hỗ trợ API response format mới:

```json
{
  "messageId": "b8df7468-7cd1-46fe-a075-dd26acf10605",
  "threadId": "d63530c4-88ab-48d6-a230-87f312e983b4", 
  "role": "user",
  "content": {
    "contentBlocks": [
      {
        "type": "text",
        "content": "ban co biet gi khong"
      }
    ],
    "replyToMessageId": "bd668eb5-4a42-414a-8a8f-8b86d3ec2faf",
    "alwaysApproveToolCall": false
  },
  "timestamp": 1749867367815,
  "updatedAt": 1749867367811
}
```

## Kết quả

Sau khi triển khai:

1. ✅ Reply messages từ history hiển thị đúng ReplyIndicator
2. ✅ Reply messages từ current conversation hiển thị đúng
3. ✅ Hỗ trợ cả API ID và local ID trong tìm kiếm
4. ✅ Debug information đầy đủ để troubleshoot
5. ✅ Test coverage đảm bảo chất lượng

## Testing

Để test các thay đổi:

1. **Reply message trong history**: Click reply trên message cũ trong history
2. **Reply message hiện tại**: Click reply trên message trong conversation hiện tại  
3. **Check console logs**: Xem debug information trong browser console
4. **Run tests**: `npm test reply-helper.test.ts`

## Troubleshooting

Nếu reply không hiển thị:

1. Check console logs cho debug information
2. Verify `replyToMessageId` có đúng format không
3. Verify message được reply có tồn tại trong messages hoặc historyMessages
4. Check metadata có chứa `replyToMessageId` không
