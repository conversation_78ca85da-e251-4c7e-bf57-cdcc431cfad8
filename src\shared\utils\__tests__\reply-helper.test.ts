/**
 * Test file để kiểm tra reply helper functions
 */

import { ChatMessage, MessageStatus } from '@/shared/types/chat-streaming.types';
import { findReplyContent, extractReplyInfo } from '../reply-helper.utils';

// Mock data theo format API mới
const mockMessages: ChatMessage[] = [
  {
    id: 'local-1',
    messageId: 'api-msg-1',
    content: 'Hello, how can I help you?',
    sender: 'assistant',
    timestamp: new Date('2024-01-01T10:00:00Z'),
    status: MessageStatus.COMPLETED,
    threadId: 'thread-1',
    metadata: {
      isFromNewAPI: true
    }
  },
  {
    id: 'local-2', 
    messageId: 'api-msg-2',
    content: 'Can you explain quantum physics?',
    sender: 'user',
    timestamp: new Date('2024-01-01T10:01:00Z'),
    status: MessageStatus.COMPLETED,
    threadId: 'thread-1',
    metadata: {
      isFromNewAPI: true
    }
  },
  {
    id: 'local-3',
    messageId: 'api-msg-3', 
    content: 'Thanks for the explanation!',
    sender: 'user',
    timestamp: new Date('2024-01-01T10:02:00Z'),
    status: MessageStatus.COMPLETED,
    threadId: 'thread-1',
    metadata: {
      isFromNewAPI: true,
      replyToMessageId: 'api-msg-1', // Reply to first message
      replyContent: 'Hello, how can I help you?' // Content from API
    }
  }
];

describe('Reply Helper Utils', () => {
  describe('findReplyContent', () => {
    it('should find reply content by messageId (API ID)', () => {
      const result = findReplyContent('api-msg-1', mockMessages);
      expect(result).toBe('Hello, how can I help you?');
    });

    it('should find reply content by local id', () => {
      const result = findReplyContent('local-2', mockMessages);
      expect(result).toBe('Can you explain quantum physics?');
    });

    it('should return undefined for non-existent message', () => {
      const result = findReplyContent('non-existent', mockMessages);
      expect(result).toBeUndefined();
    });
  });

  describe('extractReplyInfo', () => {
    it('should extract reply info from metadata', () => {
      const replyMessage = mockMessages[2]; // Message with reply
      const result = extractReplyInfo(replyMessage, mockMessages);
      
      expect(result).toEqual({
        replyToMessageId: 'api-msg-1',
        replyContent: 'Hello, how can I help you?'
      });
    });

    it('should find reply content from allMessages when not in metadata', () => {
      const messageWithoutReplyContent: ChatMessage = {
        ...mockMessages[2],
        metadata: {
          isFromNewAPI: true,
          replyToMessageId: 'api-msg-2'
          // No replyContent in metadata
        }
      };

      const result = extractReplyInfo(messageWithoutReplyContent, mockMessages);
      
      expect(result).toEqual({
        replyToMessageId: 'api-msg-2',
        replyContent: 'Can you explain quantum physics?'
      });
    });

    it('should return undefined for message without reply', () => {
      const normalMessage = mockMessages[0]; // No reply
      const result = extractReplyInfo(normalMessage, mockMessages);
      
      expect(result).toBeUndefined();
    });

    it('should find reply content from historyMessages when not found in allMessages', () => {
      const historyMessages: ChatMessage[] = [
        {
          id: 'history-1',
          messageId: 'api-msg-old',
          content: 'This is an old message',
          sender: 'assistant',
          timestamp: new Date('2024-01-01T09:00:00Z'),
          status: MessageStatus.COMPLETED,
          threadId: 'thread-1',
          metadata: {
            isFromHistory: true
          }
        }
      ];

      const messageReplyingToHistory: ChatMessage = {
        id: 'local-4',
        messageId: 'api-msg-4',
        content: 'Replying to old message',
        sender: 'user',
        timestamp: new Date('2024-01-01T10:03:00Z'),
        status: MessageStatus.COMPLETED,
        threadId: 'thread-1',
        metadata: {
          isFromNewAPI: true,
          replyToMessageId: 'api-msg-old'
          // No replyContent in metadata
        }
      };

      const result = extractReplyInfo(messageReplyingToHistory, mockMessages, historyMessages);
      
      expect(result).toEqual({
        replyToMessageId: 'api-msg-old',
        replyContent: 'This is an old message'
      });
    });
  });
});
