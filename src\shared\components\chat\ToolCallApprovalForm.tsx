/**
 * ToolCallApprovalForm Component
 * Inline message để approve/reject tool calls khi có tool_call_interrupt event
 * Hỗ trợ responsive design và đa ngôn ngữ
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { ToolCallInterruptData } from '@/shared/services/chat-sse.service';
import { toolApprovalSettingsService } from '@/shared/services/tool-approval-settings.service';

export interface ToolCallApprovalFormProps {
  /**
   * Tool call data từ interrupt event
   */
  toolCallData: ToolCallInterruptData;

  /**
   * Callback khi user chọn decision
   */
  onApprove: (decision: 'yes' | 'no' | 'always') => void;

  /**
   * Callback khi user dismiss form
   */
  onDismiss: () => void;

  /**
   * Loading state khi đang gửi approval
   */
  isLoading?: boolean;
}

/**
 * ToolCallApprovalForm Component
 */
const ToolCallApprovalForm: React.FC<ToolCallApprovalFormProps> = ({
  toolCallData,
  onApprove,
  isLoading = false
}) => {
  const { t } = useTranslation(['chat']);

  const handleDecision = (decision: 'yes' | 'no' | 'always') => {
    // Nếu chọn "always", lưu setting vào localStorage
    if (decision === 'always') {
      console.log('[ToolCallApprovalForm] 🔄 Saving always approve setting for tool:', toolCallData.toolName);

      // Có thể lưu theo tool hoặc global, tùy business logic
      // Option 1: Lưu global (always approve all tools)
      toolApprovalSettingsService.enableAlwaysApproveAllTools();

      // Option 2: Lưu theo tool cụ thể (uncomment nếu muốn)
      // toolApprovalSettingsService.enableAlwaysApproveForTool(toolCallData.toolName);

      console.log('[ToolCallApprovalForm] ✅ Always approve setting saved');
    }

    onApprove(decision);
  };

  return (
    <div className="mx-2 sm:mx-4 my-2">
      <div className="rounded-lg p-3 sm:p-4">
        {/* Header */}
        <div className="flex items-start sm:items-center justify-between mb-3 gap-2">
          <div className="flex items-start sm:items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 dark:text-gray-100 truncate">
                {t('chat:toolCall.approval.title')}
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 break-words">
                {t('chat:toolCall.approval.subtitle')}: <span className="font-medium text-primary">{toolCallData.toolName}</span>
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
          <div className="flex flex-col sm:flex-row flex-wrap gap-2 sm:gap-3">
            {/* Primary Actions */}
            <button
              onClick={() => handleDecision('yes')}
              disabled={isLoading}
              className="flex-1 min-w-0 bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 disabled:bg-green-400 dark:disabled:bg-green-500 text-white font-medium py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg text-sm sm:text-base transition-colors flex items-center justify-center gap-2 shadow-sm dark:shadow-none focus:outline-none focus:ring-2 focus:ring-green-500/50 dark:focus:ring-green-400/60"
              title={t('chat:toolCall.approval.tooltips.allow')}
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span className="hidden sm:inline text-xs sm:text-sm">{t('chat:toolCall.approval.status.approving')}</span>
                </div>
              ) : (
                <>
                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </>
              )}
            </button>

            <button
              onClick={() => handleDecision('no')}
              disabled={isLoading}
              className="flex-1 min-w-0 bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700 disabled:bg-red-400 dark:disabled:bg-red-500 text-white font-medium py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg text-sm sm:text-base transition-colors flex items-center justify-center gap-2 shadow-sm dark:shadow-none focus:outline-none focus:ring-2 focus:ring-red-500/50 dark:focus:ring-red-400/60"
              title={t('chat:toolCall.approval.tooltips.deny')}
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Always Allow */}
            <button
              onClick={() => handleDecision('always')}
              disabled={isLoading}
              className="flex-1 min-w-0 bg-primary hover:bg-primary/90 dark:bg-primary dark:hover:bg-primary/90 disabled:bg-primary/50 dark:disabled:bg-primary/40 text-white font-medium py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg text-sm sm:text-base transition-colors flex items-center justify-center gap-2 shadow-sm dark:shadow-none focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-primary/60"
              title={t('chat:toolCall.approval.tooltips.alwaysAllow')}
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolCallApprovalForm;
