/**
 * ToolCallApprovalForm Component
 * Inline message để approve/reject tool calls khi có tool_call_interrupt event
 */

import React from 'react';
import { ToolCallInterruptData } from '@/shared/services/chat-sse.service';
import { toolApprovalSettingsService } from '@/shared/services/tool-approval-settings.service';

export interface ToolCallApprovalFormProps {
  /**
   * Tool call data từ interrupt event
   */
  toolCallData: ToolCallInterruptData;

  /**
   * Callback khi user chọn decision
   */
  onApprove: (decision: 'yes' | 'no' | 'always') => void;

  /**
   * Callback khi user dismiss form
   */
  onDismiss: () => void;

  /**
   * Loading state khi đang gửi approval
   */
  isLoading?: boolean;
}

/**
 * ToolCallApprovalForm Component
 */
const ToolCallApprovalForm: React.FC<ToolCallApprovalFormProps> = ({
  toolCallData,
  onApprove,
  onDismiss,
  isLoading = false
}) => {
  const handleDecision = (decision: 'yes' | 'no' | 'always') => {
    // Nếu chọn "always", l<PERSON>u setting vào localStorage
    if (decision === 'always') {
      console.log('[ToolCallApprovalForm] 🔄 Saving always approve setting for tool:', toolCallData.toolName);

      // Có thể lưu theo tool hoặc global, tùy business logic
      // Option 1: Lưu global (always approve all tools)
      toolApprovalSettingsService.enableAlwaysApproveAllTools();

      // Option 2: Lưu theo tool cụ thể (uncomment nếu muốn)
      // toolApprovalSettingsService.enableAlwaysApproveForTool(toolCallData.toolName);

      console.log('[ToolCallApprovalForm] ✅ Always approve setting saved');
    }

    onApprove(decision);
  };

  return (
    <div className="mx-4 my-2">
      <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 shadow-sm">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                Tool Call Approval Required
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                AI wants to use: <span className="font-medium text-primary">{toolCallData.toolName}</span>
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-3">
          {/* Tool Description */}
          {toolCallData.toolDescription && (
            <p className="text-sm text-gray-700 dark:text-gray-300">
              {toolCallData.toolDescription}
            </p>
          )}

          {/* Parameters */}
          {toolCallData.parameters && Object.keys(toolCallData.parameters).length > 0 && (
            <div>
              <p className="text-xs font-medium text-gray-900 dark:text-gray-100 mb-2">
                Parameters:
              </p>
              <div className="bg-white dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-600 p-2">
                <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap overflow-x-auto">
                  {JSON.stringify(toolCallData.parameters, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Question */}
          <p className="text-sm text-gray-800 dark:text-gray-200 font-medium">
            Do you want to allow this tool to be executed?
          </p>
        </div>

        {/* Actions */}
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
          <div className="flex flex-wrap gap-2">
            {/* Primary Actions */}
            <button
              onClick={() => handleDecision('yes')}
              disabled={isLoading}
              className="flex-1 min-w-0 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-2.5 px-3 rounded-lg text-sm transition-colors flex items-center justify-center"
              title="Allow this tool call"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </button>

            <button
              onClick={() => handleDecision('no')}
              disabled={isLoading}
              className="flex-1 min-w-0 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-medium py-2.5 px-3 rounded-lg text-sm transition-colors flex items-center justify-center"
              title="Deny this tool call"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Always Allow */}
            <button
              onClick={() => handleDecision('always')}
              disabled={isLoading}
              className="w-full bg-primary hover:bg-primary/90 disabled:bg-primary/50 text-white font-medium py-2.5 px-3 rounded-lg text-sm transition-colors mt-2 flex items-center justify-center gap-2"
              title="Always allow all tool calls (saves to localStorage)"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="text-xs">Always Allow</span>
            </button>
          </div>

          {/* Clear Always Approve Setting */}
          {toolApprovalSettingsService.shouldAlwaysApprove() && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={() => {
                  toolApprovalSettingsService.disableAlwaysApproveAllTools();
                  console.log('[ToolCallApprovalForm] 🗑️ Cleared always approve setting');
                }}
                className="w-full bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white font-medium py-2.5 px-3 rounded-lg text-sm transition-colors flex items-center justify-center gap-2"
                title="Clear always allow setting"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <span className="text-xs">Clear Setting</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ToolCallApprovalForm;
