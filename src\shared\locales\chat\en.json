{"chat": {"thinking": {"title": "Thinking...", "processing": "Processing your request", "completed": "Completed", "waiting": "Waiting for response..."}, "aiThinking": "AI is thinking...", "typeSlashForMenu": "Type / for quick menu...", "sendMessage": "Send message", "stopGeneration": "Stop generation", "retry": "Retry", "reply": "Reply", "copy": "Copy", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "loading": "Loading...", "error": "An error occurred", "success": "Success", "warning": "Warning", "info": "Information", "noMessages": "No messages yet", "startConversation": "Start conversation", "placeholder": "Type your message...", "sending": "Sending...", "sent": "<PERSON><PERSON>", "received": "Received", "failed": "Failed", "retrying": "Retrying...", "connecting": "Connecting...", "connected": "Connected", "disconnected": "Disconnected", "reconnecting": "Reconnecting...", "streamError": "Stream error", "networkError": "Network error", "timeout": "Timeout", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not found", "serverError": "Server error", "unknownError": "Unknown error", "toolCall": {"approval": {"title": "Tool Call Approval Required", "subtitle": "AI wants to use", "question": "Do you want to allow this tool to be executed?", "parameters": "Parameters", "actions": {"allow": "Allow", "deny": "<PERSON><PERSON>", "alwaysAllow": "Always Allow", "clearSetting": "Clear Setting"}, "status": {"approving": "Approving...", "loading": "Loading..."}, "info": {"role": "Role", "thread": "<PERSON><PERSON><PERSON>", "alwaysAllowDescription": "Save to localStorage", "clearSettingDescription": "Clear 'Always Allow' setting"}, "tooltips": {"allow": "Allow this tool call", "deny": "Deny this tool call", "alwaysAllow": "Always allow all tool calls (save to localStorage)", "clearSetting": "Clear always allow setting", "close": "Close approval form"}}}}}